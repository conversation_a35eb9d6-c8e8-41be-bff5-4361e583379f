import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:wellfedadmin/models/course_input_models.dart';
import 'package:wellfedadmin/models/course_model.dart';
import 'package:wellfedadmin/utils/firebase.dart';
import 'package:wellfedadmin/utils/methods.dart';
import 'package:wellfedadmin/utils/theme.dart';
import 'package:wellfedadmin/views/courses/course_detail_view.dart';

class CoursesCtrl extends GetxController {
  final searchCtrl = TextEditingController();
  List<CourseModel> courses = <CourseModel>[];

  @override
  void onInit() {
    super.onInit();
    setUpCoursesStream();
  }

  setUpCoursesStream() async {
    try {
      FBFireStore.courses.snapshots().listen((event) {
        courses = event.docs
            .map((e) => CourseModel.fromJson(e.id, e.data()))
            .toList();
        update();
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  deleteCourse(BuildContext context, String courseId) async {
    try {
      await FBFireStore.courses.doc(courseId).delete();
      if (context.mounted) showAppDialog(context, "Course Deleted!");
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  duplicateCourse(BuildContext context, CourseModel course) async {
    try {
      CourseInputModel courseInputModel =
          await compute((message) => isolatedCourseMaker(course), "Message");
      courseInputModel.publishedOn = DateTime.now();
      courseInputModel.updatedOn = DateTime.now();
      courseInputModel.imageUrlCtrl.text = "";
      courseInputModel.courseId =
          'WF${await Get.find<CoursesCtrl>().getIdForCourse()}';
      await FBFireStore.courses.add(courseInputModel.toJsonData());
      if (context.mounted) {
        showAppDialog(context, "Course Duplicated!", SnackBarSeverity.success);
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  Future<String?> getIdForCourse() async {
    try {
      final sfDocRef = FBFireStore.data;
      return await FBFireStore.fs.runTransaction<String?>((transaction) async {
        return transaction.get(sfDocRef).then((sfDoc) {
          final newCourseNo = sfDoc.get("courseCount") + 1;
          transaction.update(sfDocRef, {"courseCount": newCourseNo});
          return newCourseNo.toString().padLeft(4, '0');
        });
      }).then((value) => value,
          onError: (e) => debugPrint('Error in fetching course no.: $e'));
    } catch (e) {
      debugPrint(e.toString());
      return null;
    }
  }
}
