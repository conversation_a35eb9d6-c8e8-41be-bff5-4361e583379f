// ignore_for_file: deprecated_member_use
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:wellfedadmin/controllers/courses_ctrl.dart';
import 'package:wellfedadmin/models/course_model.dart';
import 'package:wellfedadmin/models/time_duration.dart';
import 'package:wellfedadmin/views/courses/course_detail_view.dart';

class Courses extends StatelessWidget {
  const Courses({super.key});

  @override
  Widget build(BuildContext context) {
    final isMobile = MediaQuery.of(context).size.width < 768;
    return GetBuilder<CoursesCtrl>(
      init: Get.find<CoursesCtrl>(),
      builder: (controller) {
        final courseList = controller.courses
            .where((e) => e.title
                .toLowerCase()
                .contains(controller.searchCtrl.text.toLowerCase()))
            .toList();

        return Scaffold(
          body: Padding(
            padding: EdgeInsets.symmetric(
                horizontal:
                    isMobile ? 0 : 16), // consistent padding around everything
            child: Scrollbar(
              child: CustomScrollView(
                slivers: [
                  SliverToBoxAdapter(
                    child: _buildHeaderSection(
                        context, controller, courseList.length),
                  ),
                  const SliverToBoxAdapter(child: SizedBox(height: 16)),
                  courseList.isEmpty
                      ? SliverFillRemaining(
                          hasScrollBody: false,
                          child: _buildEmptyState(context),
                        )
                      : SliverToBoxAdapter(
                          child: MasonryGridView.count(
                            crossAxisCount: _getCrossAxisCount(context),
                            mainAxisSpacing: 16,
                            crossAxisSpacing: 16,
                            itemCount: courseList.length,
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            itemBuilder: (context, index) =>
                                _buildCourseCard(context, courseList[index]),
                          ),
                        ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  int _getCrossAxisCount(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width >= 1200) return 3;
    if (width >= 900) return 3;
    if (width >= 600) return 2;
    return 1;
  }

  Widget _buildHeaderSection(
      BuildContext context, CoursesCtrl controller, int totalCourses) {
    return Padding(
      padding: const EdgeInsets.symmetric(
          horizontal: 4), // smaller padding inside the global padding
      child: CoursesHeader(
        totalCourses: totalCourses,
        activeCourses: totalCourses,
        totalRevenue: 0,
        activeUsers: 0,
      ),
    );
  }

  Widget _buildCourseCard(BuildContext context, CourseModel course) {
    final isMobile = MediaQuery.of(context).size.width < 768;

    String formatDuration(TimeDuration duration) {
      return '${duration.hours}h ${duration.minutes}m';
    }

    String formattedDate(DateTime date) {
      return '${date.day}/${date.month}/${date.year}';
    }

    int totalVideos() {
      int count = 0;
      for (var chapter in course.chapters) {
        count += chapter.modules.length;
      }
      return count;
    }

    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () => {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (_) => CourseView(courseDocId: course.docId),
            ),
          ),
        },
        child: Padding(
          padding: EdgeInsets.all(isMobile ? 12 : 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Thumbnail
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.network(
                  course.imageUrl,
                  width: double.infinity,
                  height: 140,
                  fit: BoxFit.cover,
                  errorBuilder: (_, __, ___) => Container(
                    width: double.infinity,
                    height: 140,
                    color: Colors.grey[300],
                    child: const Icon(Icons.broken_image, size: 40),
                  ),
                ),
              ),
              const SizedBox(height: 12),

              // UID and availability
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'UID: ${course.courseId}',
                    style: TextStyle(fontSize: 12, color: Colors.grey[700]),
                  ),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: course.blocked
                          ? Colors.red.withOpacity(0.2)
                          : Colors.green.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      course.blocked ? 'Off' : 'On',
                      style: TextStyle(
                        color: course.blocked ? Colors.red : Colors.green,
                        fontWeight: FontWeight.w600,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),

              // Title
              Text(
                course.title,
                style: TextStyle(
                  fontSize: isMobile ? 16 : 18,
                  fontWeight: FontWeight.bold,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 12),

              // Info chips
              Wrap(
                spacing: 12,
                runSpacing: 8,
                children: [
                  _infoChip(
                      Icons.menu_book, '${course.chapters.length} Chapters'),
                  _infoChip(Icons.video_collection, '${totalVideos()} Videos'),
                  _infoChip(Icons.timer, formatDuration(course.duration)),
                  _infoChip(Icons.update,
                      'Updated: ${formattedDate(course.updatedOn)}'),
                  _infoChip(Icons.attach_money,
                      'Price: \$${course.price.toStringAsFixed(2)}'),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _infoChip(IconData icon, String label) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(6),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: Colors.grey[700]),
          const SizedBox(width: 4),
          Text(
            label,
            style: const TextStyle(fontSize: 12, color: Colors.black87),
          ),
        ],
      ),
    );
  }

  // Widget _infoChip(IconData icon, String text) {
  //   return Container(
  //     padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
  //     decoration: BoxDecoration(
  //       color: Colors.grey[200],
  //       borderRadius: BorderRadius.circular(6),
  //     ),
  //     child: Row(
  //       mainAxisSize: MainAxisSize.min,
  //       children: [
  //         Icon(icon, size: 14, color: Colors.grey[700]),
  //         const SizedBox(width: 4),
  //         Text(text,
  //             style: TextStyle(fontSize: 12, color: Colors.grey[700]),
  //             overflow: TextOverflow.ellipsis),
  //       ],
  //     ),
  //   );
  // }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.school_outlined, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 12),
          Text('No courses found',
              style: TextStyle(fontSize: 18, color: Colors.grey[600])),
          const SizedBox(height: 6),
          Text('Create your first course to get started',
              style: TextStyle(fontSize: 14, color: Colors.grey[500])),
          const SizedBox(height: 20),
          ElevatedButton.icon(
            icon: const Icon(Icons.add),
            label: const Text('Add New Course'),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (_) => const CourseView()),
              );
            },
          ),
        ],
      ),
    );
  }

  // Widget _buildStatCard({
  //   required IconData icon,
  //   required String title,
  //   required String value,
  //   required Color color,
  // }) {
  //   return Container(
  //     padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
  //     decoration: BoxDecoration(
  //       color: color.withOpacity(0.1),
  //       borderRadius: BorderRadius.circular(12),
  //     ),
  //     child: Row(
  //       children: [
  //         Icon(icon, color: color, size: 28),
  //         const SizedBox(width: 12),
  //         Column(
  //           crossAxisAlignment: CrossAxisAlignment.start,
  //           children: [
  //             Text(value,
  //                 style: TextStyle(
  //                     fontSize: 22, fontWeight: FontWeight.bold, color: color)),
  //             Text(title,
  //                 style: TextStyle(fontSize: 14, color: Colors.grey[700])),
  //           ],
  //         ),
  //       ],
  //     ),
  //   );
  // }
}

class CoursesHeader extends StatelessWidget {
  final int totalCourses;
  final int activeCourses;
  final double totalRevenue;
  final int activeUsers;

  const CoursesHeader({
    Key? key,
    required this.totalCourses,
    required this.activeCourses,
    required this.totalRevenue,
    required this.activeUsers,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isSmallScreen = MediaQuery.of(context).size.width < 600;

    if (isSmallScreen) {
      return Padding(
        padding: const EdgeInsets.all(4),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                    child: buildStatCard(Icons.school_outlined, "Total Courses",
                        "$totalCourses", Colors.blue, true)),
                const SizedBox(width: 16),
                Expanded(
                    child: buildStatCard(
                        Icons.check_circle_outline,
                        "Active Courses",
                        "$activeCourses",
                        Colors.green,
                        true)),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                    child: buildStatCard(
                        Icons.attach_money,
                        "Total Revenue",
                        "\$${totalRevenue.toStringAsFixed(2)}",
                        Colors.orange,
                        true)),
                const SizedBox(width: 16),
                Expanded(
                    child: buildStatCard(Icons.people_alt, "Active Users",
                        "$activeUsers", Colors.purple, true)),
              ],
            ),
          ],
        ),
      );
    } else {
      return Row(
        children: [
          Expanded(
              child: buildStatCard(Icons.school_outlined, "Total Courses",
                  "$totalCourses", Colors.blue, false)),
          const SizedBox(width: 16),
          Expanded(
              child: buildStatCard(Icons.check_circle_outline, "Active Courses",
                  "$activeCourses", Colors.green, false)),
          const SizedBox(width: 16),
          Expanded(
              child: buildStatCard(
                  Icons.attach_money,
                  "Total Revenue",
                  "\$${totalRevenue.toStringAsFixed(2)}",
                  Colors.orange,
                  false)),
          const SizedBox(width: 16),
          Expanded(
              child: buildStatCard(Icons.people_alt, "Active Users",
                  "$activeUsers", Colors.purple, false)),
        ],
      );
    }
  }

  Widget buildStatCard(
      IconData icon, String title, String value, Color color, bool compact) {
    return Container(
      padding:
          EdgeInsets.symmetric(horizontal: 16, vertical: compact ? 12 : 20),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Icon(icon, size: compact ? 20 : 28, color: color),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(value,
                    style: TextStyle(
                        fontSize: compact ? 18 : 22,
                        fontWeight: FontWeight.bold,
                        color: color),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis),
                Text(title,
                    style: TextStyle(
                        fontSize: compact ? 12 : 14, color: Colors.grey[700])),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
