import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:wellfedadmin/models/course_input_models.dart';
import 'package:wellfedadmin/utils/const.dart';
import 'package:wellfedadmin/utils/methods.dart';
import 'package:wellfedadmin/views/courses/match_tile.dart';
import 'package:wellfedadmin/views/courses/mcq_tile.dart';

class AssignmentTileExpander extends StatelessWidget {
  const AssignmentTileExpander(
      {super.key,
      required this.count,
      required this.index,
      required this.idx,
      required this.assign,
      required this.removeAssign,
      required this.removeAssignQuestion,
      required this.setState});
  final int count;
  final int index;
  final int idx;
  final AssignmentInputModel assign;
  final Function setState;
  final Function removeAssign;
  final Function removeAssignQuestion;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Card(
        color: Colors.grey[100],
        child: ExpansionTile(
          initiallyExpanded: true,
          trailing: IconButton(
            icon: const Icon(Icons.delete),
            onPressed: () => removeAssign(index, idx),
          ),
          title: Text(
            "Assignment ${idx + 1}",
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  StaggeredGrid.count(
                    crossAxisSpacing: 8,
                    mainAxisSpacing: 8,
                    crossAxisCount: count == 1 ? 1 : count - 1,
                    children: [
                      TextFormField(
                        controller: assign.name,
                        decoration: const InputDecoration(
                          labelText: "Title",
                          hintText: "Assignment Name",
                        ),
                        validator: (value) =>
                            value!.isEmpty ? "Required" : null,
                      ),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          Expanded(
                            child: TextFormField(
                              decoration:
                                  const InputDecoration(labelText: "Hours"),
                              initialValue: assign.duration.hours.toString(),
                              keyboardType: TextInputType.number,
                              onChanged: (value) => setState(() => assign
                                  .duration.hours = int.tryParse(value) ?? 0),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: TextFormField(
                              decoration:
                                  const InputDecoration(labelText: "Minutes"),
                              initialValue: assign.duration.minutes.toString(),
                              keyboardType: TextInputType.number,
                              onChanged: (value) => setState(() => assign
                                  .duration.minutes = int.tryParse(value) ?? 0),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: assign.minPercentage,
                        decoration: const InputDecoration(
                          labelText: "Min Passing %",
                          hintText: "Minimum percentage required",
                        ),
                        keyboardType: TextInputType.number,
                        validator: (value) =>
                            value!.isEmpty ? "Required" : null,
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Row(
                      children: [
                        FilledButton(
                          child: const Text("Add MCQ"),
                          onPressed: () {
                            assign.mcqs.add(MCQInputModel(
                                id: getRandomId(courseContentIdLen),
                                question: TextEditingController(),
                                answerIndex: 0,
                                options: List.generate(
                                    4, (index) => TextEditingController())));
                            setState(() {});
                          },
                        ),
                        const SizedBox(width: 12),
                        FilledButton(
                          child: const Text("Add Match"),
                          onPressed: () {
                            assign.matches.add(MatchInputModel(
                                id: getRandomId(courseContentIdLen),
                                question: TextEditingController(),
                                pairs: []));
                            setState(() {});
                          },
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 20),
                  ...List.generate(
                      assign.mcqs.length,
                      (ii) => MCQTileExpander(
                          count: count,
                          index: index,
                          idx: idx,
                          i: ii,
                          mcq: assign.mcqs[ii],
                          removeAssignQuestion: removeAssignQuestion,
                          setState: setState)),
                  const SizedBox(height: 20),
                  ...List.generate(
                      assign.matches.length,
                      (ii) => MatchTileExpander(
                          count: count,
                          index: index,
                          idx: idx,
                          i: ii,
                          match: assign.matches[ii],
                          removeAssignQuestion: removeAssignQuestion,
                          setState: setState)),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
