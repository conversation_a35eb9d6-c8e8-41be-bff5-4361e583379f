import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:wellfedadmin/models/course_input_models.dart';
import 'package:wellfedadmin/utils/const.dart';
import 'package:wellfedadmin/views/courses/assign_tile.dart';
import 'package:wellfedadmin/views/courses/module_tile.dart';

import '../../models/time_duration.dart';
import '../../utils/methods.dart';

class ChapterTileExpander extends StatelessWidget {
  const ChapterTileExpander({
    super.key,
    required this.count,
    required this.index,
    required this.chapter,
    required this.removeAssignQuestion,
    required this.removeModule,
    required this.removeAssign,
    required this.removeChapter,
    required this.setState,
    this.selectedTile,
    required this.onTileChange,
  });
  final int count;
  final int index;
  final ChapterInputModel chapter;
  final Function setState;
  final Function removeChapter;
  final Function removeModule;
  final Function removeAssign;
  final Function removeAssignQuestion;
  final int? selectedTile;
  final Function(int) onTileChange;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4),
      child: Card(
        color: Colors.grey[50],
        child: ExpansionTile(
          key: ValueKey<int>(selectedTile == index ? index : -1),
          onExpansionChanged: (bool isOpen) {
            if (isOpen) {
              debugPrint("Open: $index");
              onTileChange(index);
            } else {
              debugPrint("Close: $index");
              onTileChange(-1);
            }
          },
          trailing: IconButton(
            icon: const Icon(Icons.delete),
            onPressed: () => removeChapter(index),
          ),
          initiallyExpanded: index == selectedTile,
          title: Text(
            'Chapter ${index + 1}',
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  StaggeredGrid.count(
                    crossAxisSpacing: 8,
                    crossAxisCount: count == 1 ? 1 : count - 1,
                    children: [
                      TextFormField(
                        controller: chapter.name,
                        decoration: const InputDecoration(
                          labelText: "Title",
                          hintText: "Chapter Name",
                        ),
                        validator: (value) =>
                            value!.isEmpty ? "Required" : null,
                      ),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          Expanded(
                            child: TextFormField(
                              decoration:
                                  const InputDecoration(labelText: "Hours"),
                              initialValue: chapter.duration.hours.toString(),
                              keyboardType: TextInputType.number,
                              onChanged: (value) => setState(() => chapter
                                  .duration.hours = int.tryParse(value) ?? 0),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: TextFormField(
                              decoration:
                                  const InputDecoration(labelText: "Minutes"),
                              initialValue: chapter.duration.minutes.toString(),
                              keyboardType: TextInputType.number,
                              onChanged: (value) => setState(() => chapter
                                  .duration.minutes = int.tryParse(value) ?? 0),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Row(
                      children: [
                        FilledButton(
                          child: const Text("Add Module"),
                          onPressed: () {
                            chapter.modules.add(ModuleInputModel(
                                id: getRandomId(courseContentIdLen),
                                name: TextEditingController(),
                                duration: TimeDuration(hours: 0, minutes: 0),
                                content: TextEditingController(),
                                videoUrl: TextEditingController(),
                                minWatch: TextEditingController()));
                            setState(() {});
                          },
                        ),
                        const SizedBox(width: 12),
                        FilledButton(
                          child: const Text("Add Assignment"),
                          onPressed: () {
                            chapter.assignments.add(AssignmentInputModel(
                                id: getRandomId(courseContentIdLen),
                                name: TextEditingController(),
                                duration: TimeDuration(hours: 0, minutes: 0),
                                minPercentage: TextEditingController(),
                                mcqs: [],
                                matches: []));
                            setState(() {});
                          },
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 20),
                  ...List.generate(
                      chapter.modules.length,
                      (idx) => ModuleTileExpander(
                          count: count,
                          index: index,
                          idx: idx,
                          module: chapter.modules[idx],
                          removeModule: removeModule,
                          setState: setState)),
                  const SizedBox(height: 20),
                  ...List.generate(
                    chapter.assignments.length,
                    (idx) => AssignmentTileExpander(
                      count: count,
                      index: index,
                      idx: idx,
                      assign: chapter.assignments[idx],
                      removeAssign: removeAssign,
                      setState: setState,
                      removeAssignQuestion: removeAssignQuestion,
                    ),
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
