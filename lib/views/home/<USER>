import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wellfedadmin/utils/firebase.dart';
import 'package:wellfedadmin/views/courses/courses.dart';
import 'package:wellfedadmin/views/exams/courses_exams.dart';
import 'package:wellfedadmin/views/settings/settings.dart';
import 'package:wellfedadmin/widgets/admin_layout.dart';

import '../../controllers/courses_ctrl.dart';
import '../../controllers/home_ctrl.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  bool _isDialogShowing = false;
  int selectedIndex = 0;

  final List<AdminNavigationItem> navigationItems = [
    const AdminNavigationItem(
      icon: Icons.school_outlined,
      label: "Courses",
      route: "/courses",
    ),
    const AdminNavigationItem(
      icon: Icons.calendar_today_outlined,
      label: "Exam Schedules",
      route: "/exams",
    ),
    const AdminNavigationItem(
      icon: Icons.settings_outlined,
      label: "Settings",
      route: "/settings",
    ),
    const AdminNavigationItem(
      icon: Icons.logout,
      label: "Logout",
      route: "/logout",
    ),
  ];

  final List<Widget> pages = [
    const Courses(),
    const ExamCourses(),
    const SettingsPage(),
    Container(),
  ];

  final List<String> pageTitles = const [
    "Course Management",
    "Exam Schedules",
    "Settings",
    "Logout",
  ];

  @override
  void initState() {
    super.initState();
    Get.lazyPut(() => HomeCtrl());
    Get.lazyPut(() => CoursesCtrl());
  }

  @override
  Widget build(BuildContext context) {
    return AdminLayout(
      selectedIndex: selectedIndex,
      navigationItems: navigationItems,
      title: pageTitles[selectedIndex],
      onNavigationChanged: (index) {
        final navItem = navigationItems[index];

        if (navItem.label == "Logout") {
          // Use addPostFrameCallback to ensure dialog shows after the current frame
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _showLogoutDialog(context);
          });
          return;
        }

        if (selectedIndex != index) {
          setState(() {
            selectedIndex = index;
          });
        }
      },
      child: pages[selectedIndex],
    );
  }

  void _showLogoutDialog(BuildContext context) {
    if (_isDialogShowing) return; // prevent multiple dialogs
    _isDialogShowing = true;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () {
              _isDialogShowing = false; // reset flag
              Navigator.of(context).pop();
            },
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              _isDialogShowing = false; // reset flag
              Navigator.of(context).pop();
              FBAuth.auth.signOut();
            },
            child: const Text('Logout'),
          ),
        ],
      ),
    ).then((_) =>
        _isDialogShowing = false); // reset when dialog closes in any case
  }

  // Widget _buildTopBarActions() {
  //   return Padding(
  //     padding: const EdgeInsets.only(right: 24),
  //     child: ElevatedButton.icon(
  //       onPressed: () => _showLogoutDialog(context),
  //       icon: const Icon(
  //         Icons.logout,
  //         size: 18,
  //       ),
  //       label: const Text(
  //         'Logout',
  //         style: TextStyle(
  //           fontWeight: FontWeight.w500,
  //         ),
  //       ),
  //       style: ElevatedButton.styleFrom(
  //         backgroundColor: Colors.red.shade600,
  //         foregroundColor: Colors.white,
  //         padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
  //         shape: RoundedRectangleBorder(
  //           borderRadius: BorderRadius.circular(8),
  //         ),
  //         elevation: 2,
  //       ),
  //     ),
  //   );
  // }
}
