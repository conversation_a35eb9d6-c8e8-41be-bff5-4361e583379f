import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';

class FBAuth {
  static final auth = FirebaseAuth.instance;
}

class FBFireStore {
  static final fs = FirebaseFirestore.instance;
  static final sets = fs.collection('sets');
  static final data = sets.doc('data');
  static final courses = fs.collection('courses');
  static final schedules = fs.collection('schedules');
  static final users = fs.collection('users');
  static final scheduled = fs.collection('scheduled');
}

class FBStorage {
  static final fb = FirebaseStorage.instance;
  static final courseFiles = fb.ref().child('courseFiles');
}
